import React from 'react';
import { Link } from 'react-router-dom';
import { useLanguage } from '@/hooks/useLanguage';
import { useAuth } from '@/contexts/AuthContext';
import { useSaveListing } from '@/hooks/listings/useSaveListing';
import { formatCurrency } from '@/utils/dictionaries';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Heart, MapPin, Calendar } from 'lucide-react';
import { cn } from '@/lib/utils';
import { getCategoryById } from '@/utils/dictionaries/categories';

interface ListingListCardProps {
  id: string;
  title: string;
  year: number;
  make: string;
  model: string;
  price: number;
  location: string;
  imageUrl: string;
  featured?: boolean;
  createdAt?: string;
  className?: string;
  category?: string;
}

const ListingListCard: React.FC<ListingListCardProps> = ({
  id,
  title,
  year,
  make,
  model,
  price,
  location,
  imageUrl,
  featured = false,
  createdAt,
  className,
  category
}) => {
  const { t, language } = useLanguage();
  const { isAuthenticated } = useAuth();
  const { isSaved, isLoading, toggleSave } = useSaveListing(id);

  const categoryData = category ? getCategoryById(category) : null;
  const currentLanguage = language as 'en' | 'ru';

  const listingUrl = `/listings/${id}`;

  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  return (
    <div
      className={cn(
        "group bg-white dark:bg-card border rounded-lg overflow-hidden shadow-sm transition-all duration-300",
        "hover:shadow-md hover:border-primary/20",
        featured && "ring-2 ring-primary/20",
        className
      )}
    >
      <div className="flex gap-4 p-4">
        {/* Image */}
        <div className="relative flex-shrink-0">
          <Link to={listingUrl}>
            <img
              src={imageUrl}
              alt={title}
              className="w-24 h-24 sm:w-32 sm:h-32 object-cover rounded-lg"
            />
          </Link>
          {featured && (
            <Badge
              className="absolute -top-2 -right-2 bg-primary text-primary-foreground text-xs px-2 py-1"
            >
              {t('listings.featured')}
            </Badge>
          )}
        </div>

        {/* Content */}
        <div className="flex-grow min-w-0">
          <div className="flex justify-between items-start mb-2">
            <Link to={listingUrl} className="block">
              <h3 className="font-semibold text-lg text-foreground hover:text-primary transition-colors line-clamp-1">
                {title}
              </h3>
            </Link>

            <div className="flex items-center gap-2 ml-4">
              <div className="text-xl font-bold text-primary">
                {formatCurrency(price)}
              </div>

              {isAuthenticated && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={toggleSave}
                  disabled={isLoading}
                  className="h-8 w-8 p-0"
                >
                  <Heart
                    className={cn(
                      "h-4 w-4 transition-colors",
                      isSaved ? "fill-red-500 text-red-500" : "text-muted-foreground hover:text-red-500"
                    )}
                  />
                </Button>
              )}
            </div>
          </div>

          <div className="text-sm text-muted-foreground mb-2 flex items-center justify-between">
            <span>{year} {make} {model}</span>
            {categoryData && (
              <Badge variant="secondary" className="text-xs">
                {React.createElement(categoryData.icon, { className: "h-3 w-3 mr-1" })}
                {categoryData.translations[currentLanguage]}
              </Badge>
            )}
          </div>

          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <div className="flex items-center gap-1">
              <MapPin className="h-3 w-3" />
              <span className="truncate">{location}</span>
            </div>

            {createdAt && (
              <div className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                <span>{formatDate(createdAt)}</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ListingListCard;
