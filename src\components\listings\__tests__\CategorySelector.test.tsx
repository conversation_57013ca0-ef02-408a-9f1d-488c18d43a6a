import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import CategorySelector from '../CategorySelector';
import { autoPartsCategories } from '@/utils/dictionaries/categories';

// Mock the useLanguage hook
vi.mock('@/hooks/useLanguage', () => ({
  useLanguage: () => ({
    language: 'en',
    t: (key: string) => key
  })
}));

describe('CategorySelector', () => {
  it('renders all category buttons', () => {
    const mockOnCategorySelect = vi.fn();
    
    render(
      <CategorySelector
        selectedCategory=""
        onCategorySelect={mockOnCategorySelect}
      />
    );

    // Check that all categories are rendered
    autoPartsCategories.forEach(category => {
      const button = screen.getByRole('button', { 
        name: new RegExp(category.translations.en, 'i') 
      });
      expect(button).toBeInTheDocument();
    });
  });

  it('calls onCategorySelect when a category is clicked', () => {
    const mockOnCategorySelect = vi.fn();
    
    render(
      <CategorySelector
        selectedCategory=""
        onCategorySelect={mockOnCategorySelect}
      />
    );

    const firstCategory = autoPartsCategories[0];
    const button = screen.getByRole('button', { 
      name: new RegExp(firstCategory.translations.en, 'i') 
    });
    
    fireEvent.click(button);
    
    expect(mockOnCategorySelect).toHaveBeenCalledWith(firstCategory.id);
  });

  it('highlights selected category', () => {
    const mockOnCategorySelect = vi.fn();
    const selectedCategory = autoPartsCategories[0].id;
    
    render(
      <CategorySelector
        selectedCategory={selectedCategory}
        onCategorySelect={mockOnCategorySelect}
      />
    );

    const selectedButton = screen.getByRole('button', { 
      name: new RegExp(autoPartsCategories[0].translations.en, 'i') 
    });
    
    // Check that the selected button has the default variant (not outline)
    expect(selectedButton).toHaveClass('bg-primary');
  });

  it('deselects category when clicking the same category again', () => {
    const mockOnCategorySelect = vi.fn();
    const selectedCategory = autoPartsCategories[0].id;
    
    render(
      <CategorySelector
        selectedCategory={selectedCategory}
        onCategorySelect={mockOnCategorySelect}
      />
    );

    const selectedButton = screen.getByRole('button', { 
      name: new RegExp(autoPartsCategories[0].translations.en, 'i') 
    });
    
    fireEvent.click(selectedButton);
    
    expect(mockOnCategorySelect).toHaveBeenCalledWith('');
  });
});
