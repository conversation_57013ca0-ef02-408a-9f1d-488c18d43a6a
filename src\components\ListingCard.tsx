
import React, { memo } from 'react';
import { Link } from 'react-router-dom';
import { useLanguage } from '@/hooks/useLanguage';
import { cn } from '@/lib/utils';
import { useAuth } from '@/contexts/AuthContext';
import CardHeader from './listing-card/CardHeader';
import CardImage from './listing-card/CardImage';
import CardFooter from './listing-card/CardFooter';
import CardPrice from './listing-card/CardPrice';
import { useSaveListing } from '@/hooks/listings/useSaveListing';
import { getCategoryById } from '@/utils/dictionaries/categories';
import { Badge } from '@/components/ui/badge';

export type ListingCardProps = {
  id: string;
  title: string;
  year: number;
  make: string;
  model: string;
  price: number;
  location: string;
  imageUrl: string;
  featured?: boolean;
  className?: string;
  createdAt?: string;
  category?: string;
};

const ListingCard: React.FC<ListingCardProps> = ({
  id,
  title,
  year,
  make,
  model,
  price,
  location,
  imageUrl,
  featured = false,
  className,
  createdAt,
  category
}) => {
  const { t, language } = useLanguage();
  const { isAuthenticated } = useAuth();
  const { isSaved, isLoading, toggleSave } = useSaveListing(id);

  const listingUrl = `/listings/${id}`;
  const categoryData = category ? getCategoryById(category) : null;
  const currentLanguage = language as 'en' | 'ru';

  return (
    <div
      className={cn(
        "group rounded-xl overflow-hidden bg-white dark:bg-card shadow-subtle transition-all duration-300",
        "hover:shadow-elevated hover:-translate-y-1",
        featured && "ring-2 ring-primary/20",
        className
      )}
    >
      <CardImage
        id={id}
        imageUrl={imageUrl}
        title={title}
        featured={featured}
        isSaved={isSaved}
        isLoading={isLoading}
        onToggleSave={toggleSave}
        isAuthenticated={isAuthenticated}
      />

      <div className="p-4">
        <div className="flex justify-between items-start mb-2">
          <CardHeader
            title={title}
            listingUrl={listingUrl}
          />

          <CardPrice price={price} />
        </div>

        <div className="text-sm text-muted-foreground mb-3 flex items-center justify-between">
          <span>
            {year} {make} {model}
          </span>
          {categoryData && (
            <Badge variant="secondary" className="text-xs">
              {React.createElement(categoryData.icon, { className: "h-3 w-3 mr-1" })}
              {categoryData.translations[currentLanguage]}
            </Badge>
          )}
        </div>

        <CardFooter
          location={location}
          createdAt={createdAt}
        />
      </div>
    </div>
  );
};

// Memoize the component to prevent unnecessary re-renders
export default memo(ListingCard);
