
import { useState, useEffect } from 'react';
import { Listing } from '../types';
import { SortOption } from '@/components/listings/SortSelector';

export const useListingsFilter = (listings: Listing[], searchTerm: string) => {
  const [activeFilters, setActiveFilters] = useState<Record<string, any>>({});
  const [sortBy, setSortBy] = useState<SortOption>('newest');
  const [filteredListings, setFilteredListings] = useState<Listing[]>([]);

  // Update filtered listings when search term, listings, sortBy, or activeFilters change
  useEffect(() => {
    let filtered = [...listings];

    if (searchTerm.trim() !== '') {
      const lowercaseSearch = searchTerm.toLowerCase();
      filtered = filtered.filter(
        listing =>
          listing.title.toLowerCase().includes(lowercaseSearch) ||
          listing.make.toLowerCase().includes(lowercaseSearch) ||
          listing.model.toLowerCase().includes(lowercaseSearch) ||
          listing.location.toLowerCase().includes(lowercaseSearch)
      );
    }

    // Apply any active filters
    Object.entries(activeFilters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '' &&
          value !== 'all' && value !== 'all_models' &&
          value !== 'any_year_from' && value !== 'any_year_to') {
        // Handle different filter types
        if (key === 'make' && value !== 'all') {
          filtered = filtered.filter(listing => listing.make === value);
        } else if (key === 'model') {
          filtered = filtered.filter(listing => listing.model === value);
        } else if (key === 'minPrice') {
          filtered = filtered.filter(listing => Number(listing.price) >= Number(value));
        } else if (key === 'maxPrice') {
          filtered = filtered.filter(listing => Number(listing.price) <= Number(value));
        } else if (key === 'minYear') {
          filtered = filtered.filter(listing => listing.year >= Number(value));
        } else if (key === 'maxYear') {
          filtered = filtered.filter(listing => listing.year <= Number(value));
        } else if (key === 'location') {
          filtered = filtered.filter(listing =>
            listing.location.toLowerCase().includes(value.toLowerCase())
          );
        } else if (key === 'body_type') {
          filtered = filtered.filter(listing =>
            listing.body_type === value
          );
        } else if (key === 'fuel_type') {
          filtered = filtered.filter(listing =>
            listing.fuel_type === value
          );
        } else if (key === 'transmission') {
          filtered = filtered.filter(listing =>
            listing.transmission === value
          );
        } else if (key === 'color') {
          filtered = filtered.filter(listing =>
            listing.color === value
          );
        } else if (key === 'mileage_min') {
          filtered = filtered.filter(listing =>
            listing.mileage !== undefined && listing.mileage >= Number(value)
          );
        } else if (key === 'mileage_max') {
          filtered = filtered.filter(listing =>
            listing.mileage !== undefined && listing.mileage <= Number(value)
          );
        } else if (key === 'category') {
          filtered = filtered.filter(listing =>
            listing.category === value
          );
        }
      }
    });

    // Sort the filtered listings
    switch (sortBy) {
      case 'newest':
        filtered.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
        break;
      case 'oldest':
        filtered.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
        break;
      case 'price_asc':
        filtered.sort((a, b) => Number(a.price) - Number(b.price));
        break;
      case 'price_desc':
        filtered.sort((a, b) => Number(b.price) - Number(a.price));
        break;
      default:
        break;
    }

    setFilteredListings(filtered);
  }, [searchTerm, listings, sortBy, activeFilters]);

  const handleFilterApply = (filters: Record<string, any>) => {
    setActiveFilters(filters);
  };

  return {
    filteredListings,
    sortBy,
    setSortBy,
    activeFilters,
    handleFilterApply
  };
};
