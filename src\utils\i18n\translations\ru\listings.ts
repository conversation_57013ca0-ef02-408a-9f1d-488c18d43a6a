export const listings = {
  "myListings": "Мои объявления",
  "favorites": "Избранное",
  "noListingsYet": "Объявлений пока нет. Создайте сейчас!",
  "noFavoritesYet": "Избранных объявлений пока нет. Изучите объявления!",
  "exploreListings": "Просмотреть объявления",
  "title": "Заголовок",
  "make": "Марка",
  "model": "Модель",
  "year": "Год",
  "price": "Цена",
  "description": "Описание",
  "noDescriptionProvided": "Описание не предоставлено",
  "images": "Изображения",
  "updateListing": "Обновить объявление",
  "deleteListing": "Удалить объявление",
  "confirmDelete": "Подтвердить удаление",
  "deleteListingWarning": "Вы уверены, что хотите удалить это объявление? Это действие нельзя отменить.",
  "showing": "Показано",
  "of": "из",
  "results": "результатов",
  "listingsLabel": "объявлений",
  "hideFilters": "Скрыть фильтры",
  "easySearch": "Простой поиск",
  "qualityParts": "Качественные запчасти",
  "bestPrices": "Лучшие цены",
  "secureTransactions": "Безопасные сделки",
  "whyChoose": "Почему выбирают нас?",
  "readyToSell": "Готовы продать свои запчасти?",
  "carParts": "Автозапчасти",
  "engineTransmission": "Двигатель и трансмиссия",
  "viewDetails": "Посмотреть детали",
  "selectLocation": "Выберите местоположение",
  "enterLocationToSearch": "Введите местоположение для поиска запчастей рядом с вами.",
  "enterCity": "Введите город",
  "listingsFound": "объявлений найдено",
  "filterBy": "Фильтровать по",
  "featured": "В топе",
  "notFeatured": "Не в топе",
  "topListings": "Автозапчасти высшего качества",
  "featuredListings": "Избранные объявления",
  "listingsRefreshed": "Объявления обновлены",
  "refreshError": "Ошибка обновления",
  "noListingsFound": "Объявлений не найдено",
  "createFirstListing": "Создать первое объявление",
  "quickView": "Быстрый просмотр",
  "save": "Сохранить",
  "saved": "Сохранено",
  "sortBy": "Сортировать по",
  "newest": "Новые",
  "oldest": "Старые",
  "priceLowToHigh": "Цена: от низкой к высокой",
  "priceHighToLow": "Цена: от высокой к низкой",
  "location": "Расположение",
  "viewDetailsBtn": "Посмотреть детали",
  "deleteListingConfirmation": "Вы уверены, что хотите удалить это объявление? Это действие нельзя отменить.",
  "cancel": "Отмена",
  "delete": "Удалить",
  "specifications": "Характеристики",
  "contactSeller": "Связаться с продавцом",
  "signInToContactSeller": "Войдите, чтобы связаться с продавцом",
  "sellerInformation": "Информация о продавце",
  "seller": "Продавец",
  "memberSince": "Участник с",
  "responseRate": "Частота ответов",
  "averageResponseTime": "Среднее время ответа",
  "hours": "часов",
  "mapUnavailable": "Карта недоступна",
  "failedToLoadListing": "Не удалось загрузить детали объявления",
  "listingDeletedSuccessfully": "Объявление успешно удалено",
  "failedToDeleteListing": "Не удалось удалить объявление",
  "messageSent": "Сообщение успешно отправлено",
  "errorSendingMessage": "Ошибка отправки сообщения",
  "sendMessage": "Отправить сообщение",
  "call": "Позвонить",
  "sending": "Отправка...",
  "deleting": "Удаление...",
  "typeYourMessage": "Введите ваше сообщение здесь...",
  "send": "Отправить",
  "addToFavorites": "Добавить в избранное",
  "listingNotFound": "Объявление не найдено",
  "listingMayHaveBeenRemoved": "Это объявление могло быть удалено",
  "backToListings": "Назад к объявлениям",
  "sellerContact": "Контактная информация продавца",
  "contactSellerByPhone": "Вы можете напрямую позвонить продавцу по этому номеру",
  "callNow": "Позвонить сейчас",
  "yearFrom": "Год от",
  "yearTo": "Год до",
  "selectMake": "Выберите марку",
  "selectModel": "Выберите модель",
  "from": "От",
  "to": "До",
  "min": "Мин",
  "max": "Макс",
  "priceFrom": "Цена от",
  "priceTo": "Цена до",
  "mileageFrom": "Пробег от",
  "mileageTo": "Пробег до",
  "any": "Любой",
  "all": "Все",
  "applyFilters": "Применить фильтры",
  "reset": "Сбросить",
  "filter": "Фильтр",
  "filtersApplied": "Фильтры применены",
  "page": "Страница",
  "bodyType": "Тип кузова",
  "fuelType": "Тип топлива",
  "transmission": "Коробка передач",
  "color": "Цвет",
  "mileage": "Пробег",
  "selectBodyType": "Выберите тип кузова",
  "selectFuelType": "Выберите тип топлива",
  "selectTransmission": "Выберите КПП",
  "selectColor": "Выберите цвет",
  "saveSearch": "Сохранить поиск",
  "searchSaved": "Поиск сохранен",
  "notificationsEnabled": "Уведомления включены",
  "noNotifications": "Без уведомлений",
  "errorSavingSearch": "Ошибка сохранения поиска",
  "authRequired": "Требуется авторизация",
  "pleaseLoginToSaveSearch": "Пожалуйста, войдите в систему, чтобы сохранить поиск",
  "savedSearch": "Сохраненный поиск",
  "name": "Название",
  "notifications": "Уведомления",
  "category": "Категория",
  "selectCategory": "Выберите категорию",
  "categories": "Категории",
  "engineParts": "Детали двигателя",
  "transmission": "Трансмиссия",
  "brakes": "Тормозная система",
  "suspension": "Подвеска",
  "electrical": "Электрика",
  "bodyParts": "Кузовные детали",
  "interior": "Салон",
  "exterior": "Экстерьер",
  "wheelsTires": "Колеса и шины",
  "fluidsChemicals": "Жидкости и химия",
  "toolsEquipment": "Инструменты и оборудование",
  "general": "Общие",
  "notifyNewListings": "Уведомлять о новых объявлениях",
  "saveSearchDescription": "Сохраните этот поиск, чтобы получать уведомления о новых объявлениях",
  "canCreateOrAddTest": "Вы можете создать объявление или добавить тестовые.",
  "addTestListings": "Добавить тестовые объявления",
  "testListingsCreated": "Тестовые объявления успешно созданы",
  "testListingsError": "Ошибка при создании тестовых объявлений",
  "refreshingListings": "Обновление объявлений...",
  "titleRequired": "Заголовок обязателен",
  "makeRequired": "Марка обязательна",
  "modelRequired": "Модель обязательна",
  "locationRequired": "Местоположение обязательно",
  "invalidPrice": "Неверная цена",
  "invalidYear": "Неверный год",
  "missingRequiredFields": "Отсутствуют обязательные поля",
  "errorCreatingListing": "Ошибка при создании объявления",
  "errorUpdatingListing": "Ошибка при обновлении объявления",
  "listingsFetchError": "Ошибка загрузки объявлений",
  "singleColumnView": "Одноколоночный вид",
  "multiColumnView": "Многоколоночный вид",
  "listView": "Список",
  "bodyType": "Тип кузова",
  "selectBodyType": "Выберите тип кузова",
  "fuelType": "Тип топлива",
  "selectFuelType": "Выберите тип топлива",
  "transmission": "Трансмиссия",
  "selectTransmission": "Выберите трансмиссию",
  "color": "Цвет",
  "selectColor": "Выберите цвет",
  "mileage": "Пробег",
  "enterMileage": "Введите пробег",
  "additionalDetails": "Дополнительные детали",
  "vehicleDetails": "Детали автомобиля",
  "requiredField": "Обязательное поле",
  "uploadImages": "Загрузить изображения",
  "maxImagesAllowed": "Максимально допустимо {count} изображений",
  "vehicleSpecifications": "Характеристики автомобиля",
  "sellerPhone": "Телефон продавца",
  "youCanCallSeller": "Вы можете позвонить продавцу по этому номеру",
  "signInToCall": "Войдите, чтобы позвонить продавцу",
  "signInWithGoogle": "Войти с Google",
  "writeToSeller": "Написать продавцу",
  "enterYourMessage": "Введите ваше сообщение здесь...",
  "messageSentSuccessfully": "Сообщение успешно отправлено",
  "failedToDetermineSenderOrRecipient": "Не удалось определить отправителя или получателя",
  "reportListing": "Пожаловаться на объявление",
  "reportListingDescription": "Пожалуйста, укажите причину жалобы на это объявление",
  "reportReason": "Причина жалобы",
  "reportReasonPlaceholder": "Опишите причину вашей жалобы...",
  "reportSent": "Жалоба отправлена",
  "reportSentDescription": "Спасибо за вашу жалобу. Мы вскоре ее рассмотрим.",
  "reportError": "Ошибка отправки жалобы",
  "createListing": "Создать объявление",
  "searchAndFilters": "Поиск и фильтры",
  "write": "Написать",
  "characteristics": "Характеристики",
  "search": "Поиск",
  "backToHome": "Вернуться на главную",
  "latestUpdates": "Последние обновления",
  "recentlyAddedListings": "Недавно добавленные объявления",
  "refresh": "Обновить",
  "viewAll": "Показать все",
  "mustBeLoggedInToSave": "Вы должны войти в систему, чтобы сохранять объявления",
  "failedToUpdateSaveStatus": "Не удалось обновить статус сохранения",
  "savedSearches": "Сохраненные поиски",
  "noSavedSearches": "Нет сохраненных поисков",
  "saveSearchesToGetNotified": "Сохраните поиски, чтобы получать уведомления о новых объявлениях",
  "signInToSaveSearches": "Войдите, чтобы сохранять поиски",
  "applySearch": "Применить поиск",
  "deleteSearch": "Удалить поиск",
  "deleteSavedSearchWarning": "Вы уверены, что хотите удалить этот сохраненный поиск?",
  "savedSearchDeleted": "Сохраненный поиск удален",
  "errorDeletingSavedSearch": "Ошибка удаления сохраненного поиска",
  "savedSearchApplied": "Сохраненный поиск применен",
  "errorLoadingSavedSearches": "Ошибка загрузки сохраненных поисков",
  "notificationsEnabled": "Уведомления включены",
  "notificationsDisabled": "Уведомления отключены",
  "errorUpdatingNotifications": "Ошибка обновления уведомлений",
  "notificationsOn": "Уведомления вкл",
  "notificationsOff": "Уведомления выкл"
};
