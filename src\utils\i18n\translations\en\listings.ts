export const listings = {
  "myListings": "My Listings",
  "favorites": "Favorites",
  "noListingsYet": "No listings yet. Create one now!",
  "noFavoritesYet": "No favorites yet. Explore listings!",
  "exploreListings": "Explore Listings",
  "title": "Title",
  "make": "Make",
  "model": "Model",
  "year": "Year",
  "price": "Price",
  "description": "Description",
  "noDescriptionProvided": "No description provided",
  "images": "Images",
  "updateListing": "Update Listing",
  "deleteListing": "Delete Listing",
  "confirmDelete": "Confirm Delete",
  "deleteListingWarning": "Are you sure you want to delete this listing? This action cannot be undone.",
  "showing": "Showing",
  "of": "of",
  "results": "results",
  "listingsLabel": "listings",
  "hideFilters": "Hide Filters",
  "easySearch": "Easy Search",
  "qualityParts": "Quality Parts",
  "bestPrices": "Best Prices",
  "secureTransactions": "Secure Transactions",
  "whyChoose": "Why Choose Us?",
  "readyToSell": "Ready to Sell Your Parts?",
  "carParts": "Car Parts",
  "engineTransmission": "Engine & Transmission",
  "viewDetails": "View Details",
  "selectLocation": "Select Location",
  "enterLocationToSearch": "Enter a location to search for parts near you.",
  "enterCity": "Enter City",
  "listingsFound": "listings found",
  "filterBy": "Filter by",
  "featured": "Featured",
  "notFeatured": "Not Featured",
  "topListings": "Top quality auto parts",
  "featuredListings": "Featured Listings",
  "listingsRefreshed": "Listings refreshed",
  "refreshError": "Error refreshing",
  "noListingsFound": "No listings found",
  "createFirstListing": "Create first listing",
  "quickView": "Quick View",
  "save": "Save",
  "saved": "Saved",
  "sortBy": "Sort by",
  "newest": "Newest",
  "oldest": "Oldest",
  "priceLowToHigh": "Price: Low to High",
  "priceHighToLow": "Price: High to Low",
  "location": "Location",
  "viewDetailsBtn": "View Details",
  "deleteListingConfirmation": "Are you sure you want to delete this listing? This action cannot be undone.",
  "cancel": "Cancel",
  "delete": "Delete",
  "specifications": "Specifications",
  "contactSeller": "Contact Seller",
  "signInToContactSeller": "Sign in to contact seller",
  "sellerInformation": "Seller Information",
  "seller": "Seller",
  "memberSince": "Member since",
  "responseRate": "Response Rate",
  "averageResponseTime": "Average Response Time",
  "hours": "hours",
  "mapUnavailable": "Map unavailable",
  "failedToLoadListing": "Failed to load listing details",
  "listingDeletedSuccessfully": "Listing deleted successfully",
  "failedToDeleteListing": "Failed to delete listing",
  "messageSent": "Message sent successfully",
  "errorSendingMessage": "Error sending message",
  "sendMessage": "Send Message",
  "call": "Call",
  "sending": "Sending...",
  "deleting": "Deleting...",
  "typeYourMessage": "Type your message here...",
  "send": "Send",
  "addToFavorites": "Add to Favorites",
  "listingNotFound": "Listing not found",
  "listingMayHaveBeenRemoved": "This listing may have been removed",
  "backToListings": "Back to listings",
  "sellerContact": "Seller Contact Information",
  "contactSellerByPhone": "You can call the seller directly using this number",
  "callNow": "Call Now",
  "yearFrom": "Year from",
  "yearTo": "Year to",
  "selectMake": "Select make",
  "selectModel": "Select model",
  "from": "From",
  "to": "To",
  "min": "Min",
  "max": "Max",
  "priceFrom": "Price from",
  "priceTo": "Price to",
  "mileageFrom": "Mileage from",
  "mileageTo": "Mileage to",
  "any": "Any",
  "all": "All",
  "applyFilters": "Apply filters",
  "reset": "Reset",
  "filter": "Filter",
  "filtersApplied": "Filters applied",
  "page": "Page",
  "canCreateOrAddTest": "You can create one or add test listings.",
  "addTestListings": "Add Test Listings",
  "testListingsCreated": "Test listings created successfully",
  "testListingsError": "Error creating test listings",
  "refreshingListings": "Refreshing listings...",
  "listingsFetchError": "Error loading listings",
  "singleColumnView": "Single column view",
  "multiColumnView": "Multi-column view",
  "bodyType": "Body Type",
  "selectBodyType": "Select body type",
  "fuelType": "Fuel Type",
  "selectFuelType": "Select fuel type",
  "transmission": "Transmission",
  "selectTransmission": "Select transmission",
  "color": "Color",
  "selectColor": "Select color",
  "mileage": "Mileage",
  "enterMileage": "Enter mileage",
  "additionalDetails": "Additional Details",
  "vehicleDetails": "Vehicle Details",
  "requiredField": "Required field",
  "uploadImages": "Upload Images",
  "maxImagesAllowed": "Maximum {count} images allowed",
  "vehicleSpecifications": "Vehicle Specifications",
  "sellerPhone": "Seller Phone",
  "youCanCallSeller": "You can call the seller at this number",
  "signInToCall": "Sign in to call seller",
  "signInWithGoogle": "Sign in with Google",
  "writeToSeller": "Write to Seller",
  "enterYourMessage": "Enter your message here...",
  "messageSentSuccessfully": "Message sent successfully",
  "failedToDetermineSenderOrRecipient": "Failed to determine sender or recipient",
  "reportListing": "Report Listing",
  "reportListingDescription": "Please provide the reason for reporting this listing",
  "reportReason": "Report Reason",
  "reportReasonPlaceholder": "Describe the reason for your report...",
  "reportSent": "Report Sent",
  "reportSentDescription": "Thank you for your report. We will review it shortly.",
  "reportError": "Error sending report",
  "createListing": "Create Listing",
  "searchAndFilters": "Search and Filters",
  "write": "Write",
  "characteristics": "Specifications",
  "search": "Search",
  "backToHome": "Back to home",
  "latestUpdates": "Latest Updates",
  "recentlyAddedListings": "Recently added listings",
  "refresh": "Refresh",
  "viewAll": "View All",
  "mustBeLoggedInToSave": "You must be logged in to save listings",
  "failedToUpdateSaveStatus": "Failed to update save status",
  "singleColumnView": "Single Column View",
  "multiColumnView": "Multi Column View",
  "listView": "List View",
  "savedSearches": "Saved Searches",
  "noSavedSearches": "No saved searches",
  "saveSearchesToGetNotified": "Save searches to get notified about new listings",
  "category": "Category",
  "selectCategory": "Select category",
  "categories": "Categories",
  "engineParts": "Engine Parts",
  "transmission": "Transmission",
  "brakes": "Brakes",
  "suspension": "Suspension",
  "electrical": "Electrical",
  "bodyParts": "Body Parts",
  "interior": "Interior",
  "exterior": "Exterior",
  "wheelsTires": "Wheels & Tires",
  "fluidsChemicals": "Fluids & Chemicals",
  "toolsEquipment": "Tools & Equipment",
  "general": "General",
  "signInToSaveSearches": "Sign in to save searches",
  "applySearch": "Apply search",
  "deleteSearch": "Delete search",
  "deleteSavedSearchWarning": "Are you sure you want to delete this saved search?",
  "savedSearchDeleted": "Saved search deleted",
  "errorDeletingSavedSearch": "Error deleting saved search",
  "savedSearchApplied": "Saved search applied",
  "errorLoadingSavedSearches": "Error loading saved searches",
  "notificationsEnabled": "Notifications enabled",
  "notificationsDisabled": "Notifications disabled",
  "errorUpdatingNotifications": "Error updating notifications",
  "notificationsOn": "Notifications on",
  "notificationsOff": "Notifications off"
};
