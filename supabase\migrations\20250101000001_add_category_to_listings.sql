-- Add category column to listings table for automotive parts categorization
-- Categories include: Engine Parts, Transmission, Brakes, Suspension, Electrical, Body Parts, etc.

-- Add category column to listings table
ALTER TABLE listings 
ADD COLUMN category TEXT;

-- Add index for better performance when filtering by category
CREATE INDEX idx_listings_category ON listings(category);

-- Add comment to explain the category field
COMMENT ON COLUMN listings.category IS 'Category of automotive parts: engine_parts, transmission, brakes, suspension, electrical, body_parts, interior, exterior, wheels_tires, fluids_chemicals, tools_equipment, etc.';

-- Set default category for existing listings to maintain data integrity
UPDATE listings 
SET category = 'general' 
WHERE category IS NULL;

-- Add constraint to ensure category is not null for new listings
-- (We'll handle this in the application layer for now to maintain flexibility)
