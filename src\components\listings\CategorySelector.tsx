import React from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { autoPartsCategories, getCategoryName } from '@/utils/dictionaries/categories';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface CategorySelectorProps {
  selectedCategory?: string;
  onCategorySelect: (categoryId: string) => void;
  className?: string;
}

const CategorySelector: React.FC<CategorySelectorProps> = ({
  selectedCategory,
  onCategorySelect,
  className
}) => {
  const { language } = useLanguage();
  const currentLanguage = language as 'en' | 'ru';

  const handleCategoryClick = (categoryId: string) => {
    // If the same category is clicked, deselect it
    if (selectedCategory === categoryId) {
      onCategorySelect('');
    } else {
      onCategorySelect(categoryId);
    }
  };

  return (
    <div className={cn("w-full", className)}>
      <div className="flex flex-wrap gap-2 justify-center sm:justify-start">
        {autoPartsCategories.map((category) => {
          const IconComponent = category.icon;
          const isSelected = selectedCategory === category.id;
          
          return (
            <Button
              key={category.id}
              variant={isSelected ? "default" : "outline"}
              size="sm"
              onClick={() => handleCategoryClick(category.id)}
              className={cn(
                "flex items-center gap-2 transition-all duration-200 hover:scale-105",
                isSelected && "ring-2 ring-primary ring-offset-2"
              )}
            >
              <IconComponent className="h-4 w-4" />
              <span className="hidden sm:inline">
                {getCategoryName(category.id, currentLanguage)}
              </span>
            </Button>
          );
        })}
      </div>
    </div>
  );
};

export default CategorySelector;
