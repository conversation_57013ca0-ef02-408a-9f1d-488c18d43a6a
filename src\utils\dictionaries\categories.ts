import { 
  Engine, 
  Settings, 
  Disc, 
  Zap, 
  Car, 
  Armchair, 
  Palette, 
  Circle, 
  Droplets, 
  Wrench,
  Package,
  Cog
} from 'lucide-react';

// Category definitions with icons and translations
export interface Category {
  id: string;
  icon: any;
  translations: {
    en: string;
    ru: string;
  };
}

export const autoPartsCategories: Category[] = [
  {
    id: 'engine_parts',
    icon: Engine,
    translations: {
      en: 'Engine Parts',
      ru: 'Детали двигателя'
    }
  },
  {
    id: 'transmission',
    icon: Settings,
    translations: {
      en: 'Transmission',
      ru: 'Трансмиссия'
    }
  },
  {
    id: 'brakes',
    icon: Disc,
    translations: {
      en: 'Brakes',
      ru: 'Тормозная система'
    }
  },
  {
    id: 'suspension',
    icon: Cog,
    translations: {
      en: 'Suspension',
      ru: 'Подвеска'
    }
  },
  {
    id: 'electrical',
    icon: Zap,
    translations: {
      en: 'Electrical',
      ru: 'Электрика'
    }
  },
  {
    id: 'body_parts',
    icon: Car,
    translations: {
      en: 'Body Parts',
      ru: 'Кузовные детали'
    }
  },
  {
    id: 'interior',
    icon: Armchair,
    translations: {
      en: 'Interior',
      ru: 'Салон'
    }
  },
  {
    id: 'exterior',
    icon: Palette,
    translations: {
      en: 'Exterior',
      ru: 'Экстерьер'
    }
  },
  {
    id: 'wheels_tires',
    icon: Circle,
    translations: {
      en: 'Wheels & Tires',
      ru: 'Колеса и шины'
    }
  },
  {
    id: 'fluids_chemicals',
    icon: Droplets,
    translations: {
      en: 'Fluids & Chemicals',
      ru: 'Жидкости и химия'
    }
  },
  {
    id: 'tools_equipment',
    icon: Wrench,
    translations: {
      en: 'Tools & Equipment',
      ru: 'Инструменты и оборудование'
    }
  },
  {
    id: 'general',
    icon: Package,
    translations: {
      en: 'General',
      ru: 'Общие'
    }
  }
];

// Helper function to get category by id
export const getCategoryById = (id: string): Category | undefined => {
  return autoPartsCategories.find(category => category.id === id);
};

// Helper function to get category name by language
export const getCategoryName = (id: string, language: 'en' | 'ru' = 'en'): string => {
  const category = getCategoryById(id);
  return category ? category.translations[language] : id;
};

// Helper function to get all category options for select components
export const getCategoryOptions = (language: 'en' | 'ru' = 'en') => {
  return autoPartsCategories.map(category => ({
    value: category.id,
    label: category.translations[language],
    icon: category.icon
  }));
};
