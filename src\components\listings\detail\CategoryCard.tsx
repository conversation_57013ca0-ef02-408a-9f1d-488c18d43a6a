import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useLanguage } from '@/hooks/useLanguage';
import { getCategoryById } from '@/utils/dictionaries/categories';

interface CategoryCardProps {
  category?: string;
}

const CategoryCard: React.FC<CategoryCardProps> = ({ category }) => {
  const { t, language } = useLanguage();
  const currentLanguage = language as 'en' | 'ru';

  if (!category) return null;

  const categoryData = getCategoryById(category);

  if (!categoryData) return null;

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle>{t('listings.category')}</CardTitle>
      </CardHeader>
      <CardContent>
        <Badge variant="secondary" className="text-sm">
          {React.createElement(categoryData.icon, { className: "h-4 w-4 mr-2" })}
          {categoryData.translations[currentLanguage]}
        </Badge>
      </CardContent>
    </Card>
  );
};

export default CategoryCard;
