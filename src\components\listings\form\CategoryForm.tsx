import React from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { getCategoryOptions } from '@/utils/dictionaries/categories';
import { useFormContext } from './ListingFormContext';

const CategoryForm: React.FC = () => {
  const { t, language } = useLanguage();
  const { form } = useFormContext();
  const currentLanguage = language as 'en' | 'ru';

  const categoryOptions = getCategoryOptions(currentLanguage);

  return (
    <FormField
      control={form.control}
      name="category"
      render={({ field }) => (
        <FormItem>
          <FormLabel>{t('listings.category')} *</FormLabel>
          <Select value={field.value} onValueChange={field.onChange}>
            <FormControl>
              <SelectTrigger>
                <SelectValue placeholder={t('listings.selectCategory')} />
              </SelectTrigger>
            </FormControl>
            <SelectContent>
              {categoryOptions.map((option) => {
                return (
                  <SelectItem key={option.value} value={option.value}>
                    <div className="flex items-center gap-2">
                      {React.createElement(option.icon, { className: "h-4 w-4" })}
                      <span>{option.label}</span>
                    </div>
                  </SelectItem>
                );
              })}
            </SelectContent>
          </Select>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default CategoryForm;
