// Admin panel types

export type ListingStatus = "pending" | "approved" | "rejected" | "blocked" | "active";

export interface ListingWithStatus {
  id: string;
  title: string;
  make: string;
  model: string;
  year: number;
  price: number;
  location: string;
  status: ListingStatus;
  created_at: string;
  updated_at: string;
  user_id: string;
  featured: boolean | null;
  description: string | null;
  image_urls: string[] | null;
  category: string | null;
}

export type BulkAction =
  | "approve"
  | "reject"
  | "delete"
  | "block"
  | "unblock";

export interface BulkOperationResult {
  success: boolean;
  processed: number;
  failed: number;
  errors: string[];
}

export interface AnalyticsData {
  totalListings: number;
  listingsByStatus: Record<ListingStatus, number>;
  newListingsToday: number;
  newListingsThisWeek: number;
  newListingsThisMonth: number;
  activeUsers: number;
  topCategories: Array<{
    category: string;
    count: number;
  }>;
}

export interface AnalyticsFilters {
  period: "day" | "week" | "month" | "year";
  startDate?: string;
  endDate?: string;
}

export interface StatusBadgeProps {
  status: ListingStatus;
  className?: string;
}

export interface BulkActionsProps {
  selectedIds: string[];
  onAction: (action: BulkAction) => Promise<void>;
  isLoading: boolean;
}

export interface ListingTableProps {
  listings: ListingWithStatus[];
  selectedIds: string[];
  onSelectionChange: (ids: string[]) => void;
  onStatusChange: (id: string, status: ListingStatus) => Promise<void>;
  isLoading: boolean;
}
