import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { useLanguage } from '@/hooks/useLanguage';
import { PlusCircle } from 'lucide-react';
import ListingCard from '@/components/ListingCard';
import { Listing } from '@/hooks/listings/types';

interface UserListingsProps {
  listings: Listing[];
}

const UserListings: React.FC<UserListingsProps> = ({ listings }) => {
  const { t } = useLanguage();

  return (
    <div>
      <div className="mb-4 flex justify-between items-center">
        <h2 className="text-xl font-semibold">{t('profile.myListings')}</h2>
        <Button asChild>
          <a href="/create-listing" className="flex items-center">
            <PlusCircle className="mr-2 h-4 w-4" />
            {t('listings.createListing')}
          </a>
        </Button>
      </div>

      {listings.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {listings.map((listing) => (
            <ListingCard
              key={listing.id}
              id={listing.id}
              title={listing.title}
              year={listing.year}
              make={listing.make}
              model={listing.model}
              price={Number(listing.price)}
              location={listing.location}
              imageUrl={listing.image_urls[0] || 'https://images.unsplash.com/photo-1533473359331-0135ef1b58bf?auto=format&fit=crop&q=80&w=2670&ixlib=rb-4.0.3'}
              featured={listing.featured}
              createdAt={listing.created_at}
              category={listing.category}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-20 bg-muted/20 rounded-lg">
          <p className="text-muted-foreground mb-4">{t('profile.noListingsYet')}</p>
          <Button asChild>
            <a href="/create-listing">{t('listings.createListing')}</a>
          </Button>
        </div>
      )}
    </div>
  );
};

export default UserListings;
